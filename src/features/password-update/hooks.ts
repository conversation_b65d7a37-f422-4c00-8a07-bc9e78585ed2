import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PASSWORD_KEYS } from '@config/locize/password';
import { useUserId } from '@entities/user';
import { useToast } from '@hooks/system';
import type { PasswordUpdateFormType } from '@widgets/password-update/schemes';
import { useTranslation } from 'react-i18next';

import { passwordUpdateApi } from './api';

const OLD_PASSWORD_INCORRECT_ERROR_CODE = 1511;
const PASSWORD_IS_NOT_STRONG_ERROR_CODE = 1503;

export const usePasswordUpdate = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.errors);
  const { t: tP } = useTranslation(LOCIZE_NAMESPACES.password);
  const { showSuccessMessage, showErrorMessage } = useToast();

  const mutation = passwordUpdateApi.usePasswordUpdateMutation({
    onSuccess: (data) => {
      if (!data?.success) {
        showErrorMessage(
          tP(LOCIZE_PASSWORD_KEYS.passwordFailureUpdateDisclaimer),
        );
      }

      showSuccessMessage(
        tP(LOCIZE_PASSWORD_KEYS.passwordSuccessUpdateDisclaimer),
      );
    },
  });

  const { data: userId } = useUserId();

  const updatePassword = ({
    newPassword,
    oldPassword,
  }: PasswordUpdateFormType) => {
    mutation.mutateAsync({
      user_id: userId,
      new_password: newPassword,
      old_password: oldPassword,
    });
  };

  const getPasswordUpdateError = () => {
    if (!Array.isArray(mutation.error) || mutation.error.length === 0) {
      return { oldPasswordError: null, newPasswordError: null };
    }

    const error = mutation.error.find(
      (err) =>
        err.code === OLD_PASSWORD_INCORRECT_ERROR_CODE ||
        err.code === PASSWORD_IS_NOT_STRONG_ERROR_CODE,
    );

    if (!error) return { oldPasswordError: null, newPasswordError: null };

    if (error.code === OLD_PASSWORD_INCORRECT_ERROR_CODE) {
      const errorMessage = t(OLD_PASSWORD_INCORRECT_ERROR_CODE.toString());
      return { oldPasswordError: errorMessage, newPasswordError: null };
    }

    if (error.code === PASSWORD_IS_NOT_STRONG_ERROR_CODE) {
      const errorMessage = t(PASSWORD_IS_NOT_STRONG_ERROR_CODE.toString());
      return { oldPasswordError: errorMessage, newPasswordError: errorMessage };
    }

    return { oldPasswordError: null, newPasswordError: null };
  };

  return { updatePassword, getPasswordUpdateError, ...mutation };
};
