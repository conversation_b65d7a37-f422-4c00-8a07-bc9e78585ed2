import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PASSWORD_KEYS } from '@config/locize/password';
import { usePasswordUpdate } from '@features/password-update/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  PasswordUpdateFormDefaultValues,
  PasswordUpdateFormFields,
} from '../config';
import {
  PasswordUpdateFormSchema,
  type PasswordUpdateFormType,
} from '../schemes';
import styles from './PasswordUpdateForm.module.css';

export const PasswordUpdateForm = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.password);

  const { updatePassword, isPending, getPasswordUpdateError } =
    usePasswordUpdate();

  const form = useForm<PasswordUpdateFormType>({
    resolver: zodResolver(PasswordUpdateFormSchema),
    defaultValues: PasswordUpdateFormDefaultValues,
  });

  const onSubmit = ({ oldPassword, newPassword }: PasswordUpdateFormType) => {
    updatePassword({
      newPassword,
      oldPassword,
    });
  };

  const { oldPasswordError, newPasswordError } = getPasswordUpdateError();

  useEffect(() => {
    if (oldPasswordError) {
      form.setError(PasswordUpdateFormFields.OLD_PASSWORD, {
        message: oldPasswordError,
      });
    }
    if (newPasswordError) {
      form.setError(PasswordUpdateFormFields.NEW_PASSWORD, {
        message: newPasswordError,
      });
    }
  }, [oldPasswordError, newPasswordError, form]);

  const handleInputChange = (fieldName: PasswordUpdateFormFields) => {
    if (
      fieldName === PasswordUpdateFormFields.OLD_PASSWORD &&
      oldPasswordError
    ) {
      form.clearErrors(PasswordUpdateFormFields.OLD_PASSWORD);
    }
    if (
      fieldName === PasswordUpdateFormFields.NEW_PASSWORD &&
      newPasswordError
    ) {
      form.clearErrors(PasswordUpdateFormFields.NEW_PASSWORD);
    }
  };

  return (
    <Form {...form}>
      <form className={styles.form} onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          disabled={isPending}
          name={PasswordUpdateFormFields.NEW_PASSWORD}
          render={({ field }) => (
            <FormItem className={styles.formItem}>
              <FormLabel>
                {t(LOCIZE_PASSWORD_KEYS.newPasswordFieldLabel)}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="password"
                  onChange={(e) => {
                    field.onChange(e);
                    handleInputChange(PasswordUpdateFormFields.NEW_PASSWORD);
                  }}
                />
              </FormControl>
              <FormMessage className={styles.formMessage}>
                {newPasswordError ? newPasswordError : null}
              </FormMessage>
              <Typography variant="text-s" className="text-neutral-500">
                {t(LOCIZE_PASSWORD_KEYS.newPasswordFieldDescription)}
              </Typography>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          disabled={isPending}
          name={PasswordUpdateFormFields.OLD_PASSWORD}
          render={({ field }) => (
            <FormItem className={styles.formItem}>
              <FormLabel>
                {t(LOCIZE_PASSWORD_KEYS.oldPasswordFieldLabel)}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="password"
                  onChange={(e) => {
                    field.onChange(e);
                    handleInputChange(PasswordUpdateFormFields.OLD_PASSWORD);
                  }}
                />
              </FormControl>
              <FormMessage className={styles.formMessage}>
                {oldPasswordError ? oldPasswordError : null}
              </FormMessage>
              <Typography variant="text-s" className="text-neutral-500">
                {t(LOCIZE_PASSWORD_KEYS.oldPasswordFieldDescription)}
              </Typography>
            </FormItem>
          )}
        />
        <Button
          className={styles.formButton}
          loading={isPending}
          size="small"
          type="submit"
        >
          {t(LOCIZE_PASSWORD_KEYS.passwordUpdateButton)}
        </Button>
      </form>
    </Form>
  );
};
